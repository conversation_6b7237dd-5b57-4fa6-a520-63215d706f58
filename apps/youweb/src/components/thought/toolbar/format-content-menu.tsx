import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/react';
import type { ReactNode } from 'react';

import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import {
  type FormatContentMenuItem,
  FormatNodeContentMenuConfig,
} from './format-content-menu-config';

interface FormatContentMenuProps {
  editor: Editor;
  children?: ReactNode | undefined;
  onOpenChange?: (open: boolean) => void;
  contentContainer?: HTMLElement | null;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  sideOffset?: number;
  menuConfig?: FormatContentMenuItem[];
  contentClassName?: string;
  scene?: string; // 用于区分使用场景的参数
}

export const FormatContentMenu = (props: FormatContentMenuProps) => {
  const {
    editor,
    children,
    onOpenChange,
    contentContainer,
    align = 'start',
    side = 'left',
    sideOffset = 16,
    menuConfig = FormatNodeContentMenuConfig,
    contentClassName,
    scene = 'unknown',
  } = props;
  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        asChild
        className={cn('w-[13.75rem] overflow-y-auto', contentClassName)}
        side={side}
        align={align}
        sideOffset={sideOffset}
        container={contentContainer}
      >
        <div className="flex flex-col">
          <DropdownMenuLabel>Format</DropdownMenuLabel>
          {menuConfig.map((item) => (
            <DropdownMenuItem
              key={item.label}
              onClick={() => {
                // 发送埋点事件
                sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_FORMAT_CONTENT_MENU_ITEM_CLICK, {
                  scene,
                  item_label: item.label,
                });
                // 执行原有的点击逻辑
                item.onClick(editor);
              }}
            >
              {item.icon}
              {item.label}
              <DropdownMenuShortcut>{item.markdownTip}</DropdownMenuShortcut>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
