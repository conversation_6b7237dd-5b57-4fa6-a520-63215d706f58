import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { Editor } from '@tiptap/react';
import type { ReactNode } from 'react';

import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { InsertContentMenuConfig } from './insert-content-menu-config';

interface InsertContentDialogProps {
  editor: Editor;
  children?: ReactNode | undefined;
  onOpenChange?: (open: boolean) => void;
}

export const InsertContentMenu = (props: InsertContentDialogProps) => {
  const { editor, children, onOpenChange } = props;
  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent className="w-[12.5rem]" side="left" align="start" sideOffset={18}>
        <div className="flex flex-col">
          <DropdownMenuLabel>Insert</DropdownMenuLabel>
          {InsertContentMenuConfig.map((item) => (
            <DropdownMenuItem
              key={item.label}
              onClick={() => {
                // 发送埋点事件
                sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TOOLBAR_INSERT_MENU_ITEM_CLICK, {
                  item_label: item.label,
                });
                // 执行原有的点击逻辑
                item.onClick(editor);
              }}
            >
              {item.icon}
              {item.label}
              <DropdownMenuShortcut>{item.markdownTip}</DropdownMenuShortcut>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
