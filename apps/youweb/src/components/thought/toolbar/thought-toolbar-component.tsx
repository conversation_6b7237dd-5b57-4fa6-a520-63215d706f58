import { getDiffBlockManage } from '@repo/editor-ui';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { Editor } from '@tiptap/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
// import { getAIOptionsFromEditor } from '../util';
import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { ThoughtHistoryModal } from '../history/history-modal';
import { ThoughtAutoImageIcon } from '../icon/thought-auto-image-icon';
import { ThoughtFormatIcon } from '../icon/thought-format-icon';
import { ThoughtPlusIcon } from '../icon/thought-plus-icon';
import { ThoughtTranslateIcon } from '../icon/thought-translate-icon';
import { ThoughtVersionIcon } from '../icon/thought-version-icon';
import { FormatContentMenu } from './format-content-menu';
import { InsertContentMenu } from './insert-content-menu';
import { TranslateContentMenu } from './translate-content-menu';

// 常量定义
const ICON_SIZES = {
  main: 20,
  extra: 18,
} as const;

const STYLES = {
  iconStyle:
    'cursor-pointer text-muted-foreground hover:text-foreground transition-all duration-200 ease-out',
  divider: 'my-[-4px] h-[1px] w-full bg-muted',
  extraIconsContainer: 'flex flex-col gap-y-4 transition-all duration-300 ease-out',
} as const;

interface ThoughtToolbarComponentProps {
  editor: Editor;
}

// 额外图标组件
interface ExtraIconsProps {
  editor: Editor;
  showExtraIcons: boolean;
  onTranslateContentMenuChange: (open: boolean) => void;
  onItemClick: () => void;
  isTranslateContentMenuOpen: boolean;
}

const ExtraIcons = ({
  editor,
  showExtraIcons,
  onTranslateContentMenuChange,
  onItemClick,
  isTranslateContentMenuOpen,
}: ExtraIconsProps) => {
  // 计算动态样式
  const containerStyles = useMemo(() => {
    const baseClasses = STYLES.extraIconsContainer;
    const visibilityClasses = showExtraIcons
      ? 'max-h-24 translate-y-0 transform opacity-100'
      : 'max-h-0 -translate-y-2 transform opacity-0';

    return `${baseClasses} ${visibilityClasses}`;
  }, [showExtraIcons]);

  const marginTopStyle = useMemo(
    () => ({
      marginTop: showExtraIcons ? '0' : '-1rem',
    }),
    [showExtraIcons],
  );

  const onClickAutoImage = useCallback(() => {
    // 发送埋点事件
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TOOLBAR_AUTO_IMAGE_CLICK);

    // const aiOptions = getAIOptionsFromEditor(editor);
    // if (aiOptions) {
    // aiOptions.setAddCommandToListAndOpenChatPanel?.("test");
    // }
  }, [editor]);

  return (
    <div className={containerStyles} style={marginTopStyle}>
      <TooltipProvider delayDuration={500}>
        <Tooltip open={!isTranslateContentMenuOpen ? undefined : false}>
          <TooltipTrigger>
            <div onClick={onClickAutoImage}>
              <ThoughtAutoImageIcon size={ICON_SIZES.extra} className={STYLES.iconStyle} />
            </div>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Auto-Illustration</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider delayDuration={500}>
        <Tooltip open={!isTranslateContentMenuOpen ? undefined : false}>
          <TooltipTrigger>
            <TranslateContentMenu
              editor={editor}
              onOpenChange={onTranslateContentMenuChange}
              onItemClick={onItemClick}
            >
              <div>
                <ThoughtTranslateIcon size={ICON_SIZES.extra} className={`${STYLES.iconStyle}`} />
              </div>
            </TranslateContentMenu>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Translate</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export const ThoughtToolbarComponent = ({ editor }: ThoughtToolbarComponentProps) => {
  const [showExtraIcons, setShowExtraIcons] = useState(false);
  const [isInsertMenuOpen, setIsInsertMenuOpen] = useState(false);
  const [isFormatMenuOpen, setIsFormatMenuOpen] = useState(false);
  const [isTranslateContentMenuOpen, setIsTranslateContentMenuOpen] = useState(false);
  const [historyModalOpen, setHistoryModalOpen] = useState(false);
  const [hasDiffBlock, setHasDiffBlock] = useState(false);

  // 检查是否有 diff block
  const checkDiffBlock = useCallback(() => {
    const diffBlockManage = getDiffBlockManage(editor);
    if (diffBlockManage) {
      const hasBlock = diffBlockManage.checkHasDiffBlock();
      setHasDiffBlock(hasBlock);
    } else {
      setHasDiffBlock(false);
    }
  }, [editor]);

  // 监听编辑器变化
  useEffect(() => {
    checkDiffBlock();

    const handleTransaction = () => {
      checkDiffBlock();
    };

    editor.on('transaction', handleTransaction);

    return () => {
      editor.off('transaction', handleTransaction);
    };
  }, [editor, checkDiffBlock]);

  // 检查是否有菜单打开
  const hasOpenMenu = useMemo(
    () => isInsertMenuOpen || isFormatMenuOpen || isTranslateContentMenuOpen,
    [isInsertMenuOpen, isFormatMenuOpen, isTranslateContentMenuOpen],
  );

  // 事件处理函数使用 useCallback 优化
  const handleMouseLeave = useCallback(() => {
    if (!hasOpenMenu) {
      setShowExtraIcons(false);
    }
  }, [hasOpenMenu]);

  const handleMouseEnter = useCallback(() => {
    setShowExtraIcons(true);
  }, []);

  const handleItemClick = useCallback(() => {
    setShowExtraIcons(false);
  }, []);

  const handleHistoryClick = useCallback(() => {
    // 发送埋点事件
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TOOLBAR_HISTORY_CLICK);

    setHistoryModalOpen(true);
  }, []);

  // 处理 TranslateContentMenu 开关状态变化
  const handleTranslateContentMenuChange = useCallback((open: boolean) => {
    setIsTranslateContentMenuOpen(open);
    // 当菜单关闭时，隐藏 ExtraIcons
    if (!open) {
      setShowExtraIcons(false);
    }
  }, []);

  // const onClickSuggestEdits = useCallback(() => {
  //   executeAICommand({ editor, command: "suggest edits" });
  // }, [editor]);

  // 外层容器样式 - 背景和边框
  const outerContainerStyles =
    'absolute bg-card right-4 top-16 z-[10] rounded-full border border-muted bg-card shadow-md transition-all duration-300 ease-out scale-100 translate-x-0';

  // 计算内层容器样式 - 内容和透明度
  const innerContainerStyles = useMemo(() => {
    const baseClasses = 'flex flex-col gap-y-5 px-2 py-4 transition-all duration-300 ease-out';

    if (hasOpenMenu) {
      // 有菜单打开时始终显示
      return `${baseClasses} opacity-100`;
    } else {
      // 没有菜单打开时显示但透明度降低，hover 时完全显示
      return `${baseClasses} opacity-60 group-hover/material-view:opacity-100`;
    }
  }, [hasOpenMenu]);

  // 如果有 diff block，不显示工具栏
  if (hasDiffBlock) {
    return null;
  }

  return (
    <div className={outerContainerStyles} onMouseLeave={handleMouseLeave}>
      <div className={innerContainerStyles}>
        <TooltipProvider delayDuration={500}>
          <Tooltip open={!isInsertMenuOpen ? undefined : false}>
            <TooltipTrigger>
              <InsertContentMenu editor={editor} onOpenChange={setIsInsertMenuOpen}>
                <div>
                  <ThoughtPlusIcon size={ICON_SIZES.extra} className={STYLES.iconStyle} />
                </div>
              </InsertContentMenu>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Insert</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider delayDuration={500}>
          <Tooltip open={!isFormatMenuOpen ? undefined : false}>
            <TooltipTrigger>
              <FormatContentMenu editor={editor} onOpenChange={setIsFormatMenuOpen} scene="toolbar">
                <div>
                  <ThoughtFormatIcon size={ICON_SIZES.extra} className={STYLES.iconStyle} />
                </div>
              </FormatContentMenu>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Format</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider delayDuration={500}>
          <Tooltip open={!historyModalOpen ? undefined : false}>
            <TooltipTrigger>
              <div onClick={handleHistoryClick}>
                <ThoughtVersionIcon size={ICON_SIZES.extra} className={STYLES.iconStyle} />
              </div>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Versions</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className={STYLES.divider} />

        <TooltipProvider delayDuration={500}>
          <Tooltip open={!isTranslateContentMenuOpen ? undefined : false}>
            <TooltipTrigger>
              <TranslateContentMenu
                editor={editor}
                onOpenChange={handleTranslateContentMenuChange}
                onItemClick={handleItemClick}
              >
                <div onMouseEnter={handleMouseEnter}>
                  <ThoughtTranslateIcon size={ICON_SIZES.extra} className={STYLES.iconStyle} />
                </div>
              </TranslateContentMenu>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Translate</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <ThoughtHistoryModal
        editor={editor}
        open={historyModalOpen}
        onOpenChange={setHistoryModalOpen}
      />
    </div>
  );
};
