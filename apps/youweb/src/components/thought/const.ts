export enum EDITOR_EKY_MAP {
  Enter = 'Enter',
}

export enum SelectionType {
  CURSOR = 'cursor',
  SELECTION = 'selection',
}

// 导航键和功能键列表，这些键不应该触发数据更新
export const NAVIGATION_KEYS = [
  'ArrowUp',
  'ArrowDown',
  'ArrowLeft',
  'ArrowRight',
  'Home',
  'End',
  'PageUp',
  'PageDown',
  'Tab',
  'Escape',
  'CapsLock',
  'Shift',
  'Control',
  'Alt',
  'Meta',
  'ContextMenu',
] as const;

// 检查是否为导航键或功能键
export const isNavigationOrFunctionKey = (key: string): boolean => {
  return NAVIGATION_KEYS.includes(key as (typeof NAVIGATION_KEYS)[number]) || key.startsWith('F');
};

export const THOUGHT_EVENT_REPORT_KEY = {
  THOUGHT_TOOLBAR_TRANSLATE_CONTENT_MENU_CLICK: 'thought_toolbar_translate_content_menu_click',
  THOUGHT_HISTORY_MODAL_OPEN: 'thought_history_modal_open',
  THOUGHT_HISTORY_MODAL_ADD_VERSION: 'thought_history_modal_add_version',
  // Text Menu 相关埋点
  THOUGHT_TEXT_MENU_ASK_AI_CLICK: 'thought_text_menu_ask_ai_click',
  THOUGHT_TEXT_MENU_BOLD_CLICK: 'thought_text_menu_bold_click',
  THOUGHT_TEXT_MENU_LINK_CLICK: 'thought_text_menu_link_click',
  // Format Content Menu 相关埋点
  THOUGHT_FORMAT_CONTENT_MENU_ITEM_CLICK: 'thought_format_content_menu_item_click',
  // Toolbar 相关埋点
  THOUGHT_TOOLBAR_INSERT_MENU_ITEM_CLICK: 'thought_toolbar_insert_menu_item_click',
  THOUGHT_TOOLBAR_HISTORY_CLICK: 'thought_toolbar_history_click',
  THOUGHT_TOOLBAR_AUTO_IMAGE_CLICK: 'thought_toolbar_auto_image_click',
  // History 相关埋点
  THOUGHT_HISTORY_RESTORE_CLICK: 'thought_history_restore_click',
  THOUGHT_HISTORY_FILTER_CLICK: 'thought_history_filter_click',
  THOUGHT_HISTORY_VERSION_SELECT: 'thought_history_version_select',
  THOUGHT_HISTORY_VERSION_DELETE: 'thought_history_version_delete',
  THOUGHT_HISTORY_SAVE_VERSION: 'thought_history_save_version',
  // Add History Modal 相关埋点
  THOUGHT_ADD_HISTORY_MODAL_OPEN: 'thought_add_history_modal_open',
  THOUGHT_ADD_HISTORY_MODAL_SAVE: 'thought_add_history_modal_save',
  THOUGHT_ADD_HISTORY_MODAL_SHOW_ALL: 'thought_add_history_modal_show_all',
  // Ask AI Modal 相关埋点
  THOUGHT_ASK_AI_MODAL_OPEN: 'thought_ask_ai_modal_open',
  THOUGHT_ASK_AI_MODAL_SUBMIT: 'thought_ask_ai_modal_submit',
  // History Content Menu 相关埋点
  THOUGHT_HISTORY_CONTENT_MENU_ADD_VERSION: 'thought_history_content_menu_add_version',
  THOUGHT_HISTORY_CONTENT_MENU_VIEW_HISTORY: 'thought_history_content_menu_view_history',
};
