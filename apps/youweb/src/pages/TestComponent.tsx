'use client';

import { But<PERSON> } from '@repo/ui/components/ui/button';
import {
  ChevronDown,
  Plus,
  Search,
  Settings,
  X,
  Check,
  AlertCircle,
  Home,
  User,
  Heart,
} from 'lucide-react';

export default function TestComponent() {
  const variants = ['default', 'secondary', 'outline', 'destructive', 'ghost'] as const;
  const sizes = ['xxs', 'xs', 'sm', 'default', 'lg'] as const;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-8 p-8 bg-gray-50">
      <h1 className="text-3xl font-bold">Button Icon Test</h1>
      
      <div className="w-full max-w-6xl space-y-8">
        {/* Icon-only buttons */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Icon-only Buttons (Should be circular)</h2>
          <div className="space-y-4">
            {variants.map((variant) => (
              <div key={variant} className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700 capitalize">{variant} variant:</h3>
                <div className="flex items-center gap-3">
                  {sizes.map((size) => (
                    <div key={size} className="flex flex-col items-center gap-1">
                      <Button variant={variant} size={size}>
                        <Plus />
                      </Button>
                      <span className="text-xs text-gray-500">{size}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Different icons */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Different Icons (all default size)</h2>
          <div className="space-y-4">
            {variants.map((variant) => (
              <div key={variant} className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700 capitalize">{variant} variant:</h3>
                <div className="flex items-center gap-3">
                  <Button variant={variant} size="default">
                    <Plus />
                  </Button>
                  <Button variant={variant} size="default">
                    <Search />
                  </Button>
                  <Button variant={variant} size="default">
                    <Settings />
                  </Button>
                  <Button variant={variant} size="default">
                    <X />
                  </Button>
                  <Button variant={variant} size="default">
                    <Check />
                  </Button>
                  <Button variant={variant} size="default">
                    <AlertCircle />
                  </Button>
                  <Button variant={variant} size="default">
                    <Home />
                  </Button>
                  <Button variant={variant} size="default">
                    <User />
                  </Button>
                  <Button variant={variant} size="default">
                    <Heart />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Buttons with text and icon */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Buttons with Text and Icon (Should NOT be circular)</h2>
          <div className="space-y-4">
            {variants.map((variant) => (
              <div key={variant} className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700 capitalize">{variant} variant:</h3>
                <div className="flex items-center gap-3">
                  {sizes.map((size) => (
                    <Button key={size} variant={variant} size={size}>
                      <Plus />
                      Add Item
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Icon variant (existing special case) */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Icon Variant (Special case)</h2>
          <div className="flex items-center gap-3">
            {sizes.map((size) => (
              <div key={size} className="flex flex-col items-center gap-1">
                <Button variant="icon" size={size}>
                  <Settings />
                </Button>
                <span className="text-xs text-gray-500">{size}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Size comparison */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Size Comparison (all default variant)</h2>
          <div className="flex items-center gap-4">
            <div className="flex flex-col items-center gap-1">
              <Button variant="default" size="xxs">
                <Plus />
              </Button>
              <span className="text-xs text-gray-500">xxs (3px)</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <Button variant="default" size="xs">
                <Plus />
              </Button>
              <span className="text-xs text-gray-500">xs (3px)</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <Button variant="default" size="sm">
                <Plus />
              </Button>
              <span className="text-xs text-gray-500">sm (3px)</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <Button variant="default" size="default">
                <Plus />
              </Button>
              <span className="text-xs text-gray-500">default (6px)</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <Button variant="default" size="lg">
                <Plus />
              </Button>
              <span className="text-xs text-gray-500">lg (8px)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}