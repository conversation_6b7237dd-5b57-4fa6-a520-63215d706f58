'use client';

import { Slot } from '@radix-ui/react-slot';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { cn } from '@repo/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import * as React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-[40px] font-normal transition-all disabled:pointer-events-none  [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed",
  {
    variants: {
      variant: {
        default: 'bg-brand text-brand-foreground hover:opacity-80 active:bg-brand',
        secondary: 'bg-muted text-foreground hover:bg-card-snips active:bg-divider',
        outline:
          'border bg-card hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:bg-card-snips disabled:text-muted-foreground disabled:border-bg-border',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/80 active:bg-destructive',
        ghost:
          'bg-transparent hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:text-muted-foreground',
        icon: '!p-1 text-accent-foreground hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:text-muted-foreground rounded-md',
      },
      size: {
        default: 'py-1.5 px-4 has-[>svg]:px-3 text-sm leading-5 gap-1 [&_svg]:!size-[16px]',
        sm: 'py-[3px] px-2.5 has-[>svg]:px-2 text-xs leading-4.5 gap-0.5 [&_svg]:!size-[14px]',
        xs: 'py-[3px] px-2.5 has-[>svg]:px-2 text-xs leading-4.5 gap-0.5 [&_svg]:!size-[12px]',
        xxs: 'py-[3px] px-2.5 has-[>svg]:px-2 text-xs leading-4.5 gap-0.5 [&_svg]:!size-[10px]',
        lg: 'py-2 px-6 has-[>svg]:px-5 text-base leading-6 gap-1.5 [&_svg]:!size-[20px]',
        icon: 'h-10 w-10',
      },
    },
    compoundVariants: [
      // Icon variant specific styles
      {
        variant: 'icon',
        className: 'gap-0',
      },
      // Equal padding for icon-only buttons
      {
        variant: ['default', 'secondary', 'outline', 'destructive', 'ghost'],
        size: ['sm', 'xs', 'xxs'],
        className: '[&:has(>:only-child)]:has(>svg):!p-[3px]',
      },
      {
        variant: ['default', 'secondary', 'outline', 'destructive', 'ghost'],
        size: 'default',
        className: '[&:has(>:only-child)]:has(>svg):!p-1.5',
      },
      {
        variant: ['default', 'secondary', 'outline', 'destructive', 'ghost'],
        size: 'lg',
        className: '[&:has(>:only-child)]:has(>svg):!p-2',
      },
    ],
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'onClick'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  async?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => any | Promise<any>;
  trackEventName?: string;
  trackEventParams?: Record<string, any>;
  iconSize?: number | string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading: loadingProp,
      async = false,
      children,
      onClick,
      trackEventName,
      trackEventParams,
      disabled,
      iconSize,
      ...props
    },
    ref,
  ) => {
    const [internalLoading, setInternalLoading] = React.useState(false);
    const loading = loadingProp ?? internalLoading;

    const { trackButtonClick } = useTrackActions();

    const handleClick = React.useCallback(
      async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        if (trackEventName) {
          trackButtonClick(trackEventName, trackEventParams);
        }

        if (!onClick) return;

        if (async) {
          try {
            setInternalLoading(true);
            const result = onClick(event);
            if (result && typeof result === 'object' && 'then' in result) {
              await result;
            }
          } catch (error) {
            console.error('Button async handler error:', error);
          } finally {
            setInternalLoading(false);
          }
        } else {
          onClick(event);
        }
      },
      [onClick, async, trackEventName, trackEventParams, trackButtonClick],
    );

    const Comp = asChild ? Slot : 'button';

    // Create dynamic icon size styles when iconSize is provided
    const iconSizeStyles = iconSize
      ? ({
          '--icon-size': typeof iconSize === 'number' ? `${iconSize}px` : iconSize,
        } as React.CSSProperties)
      : {};

    const dynamicIconSizeClass = iconSize ? '[&_svg]:!size-[var(--icon-size)]' : '';

    return (
      <Comp
        data-slot="button"
        className={cn(buttonVariants({ variant, size, className }), dynamicIconSizeClass)}
        style={iconSizeStyles}
        ref={ref}
        disabled={disabled || loading}
        onClick={handleClick}
        {...props}
      >
        {loading && <Loader2 className="mr-2 animate-spin" />}
        {children}
      </Comp>
    );
  },
);
Button.displayName = 'Button';

const ButtonWithTooltip = React.forwardRef<
  HTMLButtonElement,
  { tooltip: React.ReactNode } & ButtonProps
>((props, ref) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <Button variant="ghost" className={'h-5 w-5 rounded-full'} {...props} ref={ref} />
        </TooltipTrigger>
        <TooltipContent>{props.tooltip}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});
ButtonWithTooltip.displayName = 'ButtonWithTooltip';

export { Button, buttonVariants, ButtonWithTooltip };
